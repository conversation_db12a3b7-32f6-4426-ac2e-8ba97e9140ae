/**
 * WebSocket功能验证脚本
 * 在小程序控制台中运行此脚本来验证所有功能
 */

// 验证WebSocket管理器是否正确加载
function verifyWebSocketManager() {
  console.log('=== WebSocket管理器验证 ===');
  
  try {
    const app = getApp();
    const wsManager = app.getWebSocketManager();
    
    if (!wsManager) {
      console.error('❌ WebSocket管理器未找到');
      return false;
    }
    
    console.log('✅ WebSocket管理器加载成功');
    
    // 验证TOPICS是否存在
    if (!wsManager.TOPICS) {
      console.error('❌ TOPICS未定义');
      return false;
    }
    
    console.log('✅ TOPICS定义正确:', wsManager.TOPICS);
    
    // 验证必要的方法是否存在
    const requiredMethods = [
      'init', 'connect', 'close', 'sendMessage', 
      'sendPrivateMessage', 'queryPrivateMessage',
      'getStats', 'isConnected', 'on', 'off'
    ];
    
    for (const method of requiredMethods) {
      if (typeof wsManager[method] !== 'function') {
        console.error(`❌ 方法 ${method} 不存在`);
        return false;
      }
    }
    
    console.log('✅ 所有必要方法都存在');
    return true;
    
  } catch (error) {
    console.error('❌ WebSocket管理器验证失败:', error);
    return false;
  }
}

// 验证配置是否正确
function verifyConfiguration() {
  console.log('=== 配置验证 ===');
  
  try {
    const app = getApp();
    const wsManager = app.getWebSocketManager();
    const stats = wsManager.getStats();
    
    console.log('✅ 配置信息:', {
      serverUrl: stats.serverUrl,
      heartbeatInterval: stats.heartbeatInterval,
      tokenCheckInterval: stats.tokenCheckInterval,
      maxReconnectAttempts: stats.maxReconnectAttempts
    });
    
    // 验证TOPICS常量
    const expectedTopics = [
      'SEND_PRIVATE_MESSAGE',
      'QUERY_PRIVATE_MESSAGE', 
      'SYSTEM_NOTIFICATION',
      'HEARTBEAT',
      'HEARTBEAT_RESPONSE',
      'USER_STATUS',
      'GROUP_MESSAGE'
    ];
    
    for (const topic of expectedTopics) {
      if (!wsManager.TOPICS[topic]) {
        console.error(`❌ TOPICS.${topic} 未定义`);
        return false;
      }
    }
    
    console.log('✅ 所有TOPICS常量都已定义');
    return true;
    
  } catch (error) {
    console.error('❌ 配置验证失败:', error);
    return false;
  }
}

// 验证token状态管理
function verifyTokenManagement() {
  console.log('=== Token状态管理验证 ===');
  
  try {
    const app = getApp();
    const wsManager = app.getWebSocketManager();
    
    // 获取token状态
    const tokenStatus = wsManager.getTokenStatus();
    console.log('✅ Token状态:', tokenStatus);
    
    // 手动触发token检查
    wsManager.forceTokenCheck();
    console.log('✅ 手动token检查执行成功');
    
    return true;
    
  } catch (error) {
    console.error('❌ Token状态管理验证失败:', error);
    return false;
  }
}

// 验证事件系统
function verifyEventSystem() {
  console.log('=== 事件系统验证 ===');
  
  try {
    const app = getApp();
    const wsManager = app.getWebSocketManager();
    
    // 测试事件监听
    const testListener = (data) => {
      console.log('✅ 测试事件触发:', data);
    };
    
    // 添加监听器
    wsManager.on('tokenChange', testListener);
    console.log('✅ 事件监听器添加成功');
    
    // 移除监听器
    wsManager.off('tokenChange', testListener);
    console.log('✅ 事件监听器移除成功');
    
    return true;
    
  } catch (error) {
    console.error('❌ 事件系统验证失败:', error);
    return false;
  }
}

// 验证消息发送功能
function verifyMessageSending() {
  console.log('=== 消息发送功能验证 ===');
  
  try {
    const app = getApp();
    
    // 测试便捷方法
    console.log('测试发送私聊消息...');
    const sendResult = app.sendPrivateMessage('text', 'test message', 'test_receiver');
    console.log('✅ 发送私聊消息方法调用成功，结果:', sendResult);
    
    console.log('测试查询消息历史...');
    const queryResult = app.queryPrivateMessage('test_receiver');
    console.log('✅ 查询消息历史方法调用成功，结果:', queryResult);
    
    return true;
    
  } catch (error) {
    console.error('❌ 消息发送功能验证失败:', error);
    return false;
  }
}

// 验证连接状态
function verifyConnectionStatus() {
  console.log('=== 连接状态验证 ===');
  
  try {
    const app = getApp();
    const wsManager = app.getWebSocketManager();
    
    // 获取详细状态
    const stats = wsManager.getStats();
    console.log('✅ 连接统计信息:', stats);
    
    // 检查连接状态
    const isConnected = app.isWebSocketConnected();
    console.log('✅ 连接状态检查:', isConnected);
    
    return true;
    
  } catch (error) {
    console.error('❌ 连接状态验证失败:', error);
    return false;
  }
}

// 运行所有验证
function runAllVerifications() {
  console.log('🚀 开始WebSocket功能验证...\n');
  
  const verifications = [
    verifyWebSocketManager,
    verifyConfiguration,
    verifyTokenManagement,
    verifyEventSystem,
    verifyMessageSending,
    verifyConnectionStatus
  ];
  
  let passedCount = 0;
  
  for (const verification of verifications) {
    try {
      if (verification()) {
        passedCount++;
      }
    } catch (error) {
      console.error('验证过程中发生错误:', error);
    }
    console.log(''); // 空行分隔
  }
  
  console.log(`🎯 验证完成: ${passedCount}/${verifications.length} 项通过`);
  
  if (passedCount === verifications.length) {
    console.log('🎉 所有验证都通过！WebSocket功能正常。');
  } else {
    console.log('⚠️ 部分验证未通过，请检查相关功能。');
  }
}

// 导出验证函数（在控制台中使用）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runAllVerifications,
    verifyWebSocketManager,
    verifyConfiguration,
    verifyTokenManagement,
    verifyEventSystem,
    verifyMessageSending,
    verifyConnectionStatus
  };
}

// 如果在浏览器环境中，自动运行验证
if (typeof window !== 'undefined') {
  // 延迟执行，确保app已加载
  setTimeout(runAllVerifications, 1000);
}
