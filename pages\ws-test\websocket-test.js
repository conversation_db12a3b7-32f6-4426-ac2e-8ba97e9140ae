// pages/ws-test/websocket-test.js
Page({
  data: {
    isConnected: false,
    connectionStatus: '未连接',
    messages: [],
    inputText: '',
    receiverId: '100000', // 测试用的接收者ID
    stats: {}
  },

  onLoad: function (options) {
    console.log('WebSocket测试页面加载');
    
    // 获取WebSocket管理器
    const app = getApp();
    this.websocketManager = app.getWebSocketManager();
    
    // 检查初始连接状态
    this.updateConnectionStatus();
    
    // 设置WebSocket事件监听
    this.setupWebSocketListeners();
    
    // 定期更新统计信息
    this.startStatsUpdate();
  },

  onShow: function () {
    // 页面显示时更新连接状态
    this.updateConnectionStatus();
  },

  onUnload: function () {
    // 停止统计信息更新
    if (this.statsTimer) {
      clearInterval(this.statsTimer);
    }
  },

  // 设置WebSocket事件监听
  setupWebSocketListeners: function () {
    if (!this.websocketManager) {
      console.error('WebSocket管理器未找到');
      return;
    }

    // 监听连接打开
    this.websocketManager.on('open', () => {
      console.log('测试页面: WebSocket连接已打开');
      this.updateConnectionStatus();
      this.addMessage('系统', '连接已建立', 'system');
    });

    // 监听连接关闭
    this.websocketManager.on('close', () => {
      console.log('测试页面: WebSocket连接已关闭');
      this.updateConnectionStatus();
      this.addMessage('系统', '连接已关闭', 'system');
    });

    // 监听连接错误
    this.websocketManager.on('error', (error) => {
      console.error('测试页面: WebSocket连接错误', error);
      this.updateConnectionStatus();
      this.addMessage('系统', `连接错误: ${error.errMsg || '未知错误'}`, 'error');
    });

    // 监听重连事件
    this.websocketManager.on('reconnect', (result) => {
      console.log('测试页面: WebSocket重连事件', result);
      if (result.success) {
        this.addMessage('系统', `重连成功 (尝试次数: ${result.attempts})`, 'system');
      } else {
        this.addMessage('系统', `重连失败: ${result.reason}`, 'error');
      }
      this.updateConnectionStatus();
    });
  },

  // 处理WebSocket消息（由app.js调用）
  onWebSocketMessage: function (topic, data) {
    console.log('测试页面收到WebSocket消息', { topic, data });
    
    switch (topic) {
      case 'send_private_message':
        this.addMessage('接收', `${data.content} (来自: ${data.senderId})`, 'received');
        break;
      case 'query_private_message':
        this.addMessage('系统', `查询到 ${Array.isArray(data) ? data.length : 0} 条历史消息`, 'system');
        break;
      default:
        this.addMessage('系统', `收到消息: ${topic}`, 'system');
    }
  },

  // 更新连接状态
  updateConnectionStatus: function () {
    const app = getApp();
    const isConnected = app.isWebSocketConnected();
    const manager = this.websocketManager;
    
    let status = '未知';
    if (manager) {
      const readyState = manager.getReadyState();
      switch (readyState) {
        case 0: status = '连接中'; break;
        case 1: status = '已连接'; break;
        case 2: status = '关闭中'; break;
        case 3: status = '已关闭'; break;
      }
    }

    this.setData({
      isConnected: isConnected,
      connectionStatus: status
    });
  },

  // 开始统计信息更新
  startStatsUpdate: function () {
    this.updateStats();
    this.statsTimer = setInterval(() => {
      this.updateStats();
    }, 2000);
  },

  // 更新统计信息
  updateStats: function () {
    if (this.websocketManager) {
      const stats = this.websocketManager.getStats();
      this.setData({ stats });
    }
  },

  // 添加消息到列表
  addMessage: function (sender, content, type = 'info') {
    const messages = this.data.messages;
    messages.push({
      id: Date.now(),
      sender: sender,
      content: content,
      type: type,
      time: new Date().toLocaleTimeString()
    });
    
    // 限制消息数量
    if (messages.length > 50) {
      messages.shift();
    }
    
    this.setData({ messages });
  },

  // 发送测试消息
  sendTestMessage: function () {
    const { inputText, receiverId } = this.data;
    
    if (!inputText.trim()) {
      wx.showToast({
        title: '请输入消息内容',
        icon: 'none'
      });
      return;
    }

    const app = getApp();
    const success = app.sendPrivateMessage('text', inputText, receiverId);
    
    if (success) {
      this.addMessage('发送', inputText, 'sent');
      this.setData({ inputText: '' });
    } else {
      this.addMessage('系统', '消息发送失败', 'error');
    }
  },

  // 查询消息历史
  queryMessages: function () {
    const app = getApp();
    const success = app.queryPrivateMessage(this.data.receiverId);
    
    if (success) {
      this.addMessage('系统', '正在查询消息历史...', 'system');
    } else {
      this.addMessage('系统', '查询请求发送失败', 'error');
    }
  },

  // 手动重连
  reconnect: function () {
    if (this.websocketManager) {
      this.addMessage('系统', '手动触发重连...', 'system');
      this.websocketManager.close();
      setTimeout(() => {
        this.websocketManager.connect();
      }, 1000);
    }
  },

  // 清空消息
  clearMessages: function () {
    this.setData({ messages: [] });
  },

  // 输入框内容变化
  onInputChange: function (e) {
    this.setData({
      inputText: e.detail.value
    });
  },

  // 接收者ID变化
  onReceiverIdChange: function (e) {
    this.setData({
      receiverId: e.detail.value
    });
  },

  // 发送心跳测试
  sendHeartbeat: function () {
    if (this.websocketManager && this.websocketManager.isConnected()) {
      this.websocketManager.sendHeartbeat();
      this.addMessage('系统', '手动发送心跳包', 'system');
    } else {
      this.addMessage('系统', '连接未建立，无法发送心跳', 'error');
    }
  },

  // 获取连接统计
  getConnectionStats: function () {
    if (this.websocketManager) {
      const stats = this.websocketManager.getStats();
      const statsText = JSON.stringify(stats, null, 2);
      
      wx.showModal({
        title: '连接统计',
        content: statsText,
        showCancel: false,
        confirmText: '确定'
      });
    }
  }
});
