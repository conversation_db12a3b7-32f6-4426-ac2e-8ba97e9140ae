/**
 * WebSocket管理工具类
 * 提供WebSocket连接管理、消息发送接收、断线重连、心跳检测等功能
 *
 * 主要功能：
 * 1. WebSocket连接管理（建立、关闭、状态监控）
 * 2. 消息发送和接收处理
 * 3. 断线重连机制（指数退避算法）
 * 4. 心跳保活机制
 * 5. 消息队列缓冲
 * 6. 与现有token系统集成
 */

const WebSocketConfig = require('./websocket-config');

class WebSocketManager {
  constructor() {
    // 直接使用基础配置，不区分环境
    this.config = {
      server: WebSocketConfig.server,
      reconnect: WebSocketConfig.reconnect,
      heartbeat: WebSocketConfig.heartbeat,
      message: WebSocketConfig.message,
      logging: WebSocketConfig.logging,
      security: WebSocketConfig.security,
      performance: WebSocketConfig.performance
    };

    // 验证配置
    const validation = WebSocketConfig.validateConfig(this.config);
    if (!validation.valid) {
      console.error('WebSocketManager: 配置验证失败', validation.errors);
      throw new Error('WebSocket配置无效: ' + validation.errors.join(', '));
    }

    // WebSocket连接实例
    this.socketTask = null;

    // 连接状态枚举
    this.READY_STATE = {
      CONNECTING: 0,
      OPEN: 1,
      CLOSING: 2,
      CLOSED: 3
    };

    // 当前连接状态
    this.readyState = this.READY_STATE.CLOSED;

    // 重连配置（从配置文件读取）
    this.reconnectConfig = {
      maxAttempts: this.config.reconnect.maxAttempts,
      currentAttempts: 0,
      baseDelay: this.config.reconnect.baseDelay,
      maxDelay: this.config.reconnect.maxDelay,
      isReconnecting: false,
      useExponentialBackoff: this.config.reconnect.useExponentialBackoff
    };

    // 心跳配置（从配置文件读取）
    this.heartbeatConfig = {
      interval: this.config.heartbeat.interval,
      timeout: this.config.heartbeat.timeout,
      timer: null,
      timeoutTimer: null,
      missedCount: 0,
      maxMissed: this.config.heartbeat.maxMissed
    };

    // 消息队列
    this.messageQueue = [];
    this.maxQueueLength = this.config.message.maxQueueLength;

    // 事件监听器
    this.eventListeners = {
      open: [],
      message: [],
      close: [],
      error: [],
      reconnect: []
    };

    // 消息主题常量
    this.TOPICS = this.config.message.topics;

    // 初始化标志
    this.isInitialized = false;

    // 绑定方法上下文
    this.handleOpen = this.handleOpen.bind(this);
    this.handleMessage = this.handleMessage.bind(this);
    this.handleClose = this.handleClose.bind(this);
    this.handleError = this.handleError.bind(this);

    // 日志配置
    this.logging = this.config.logging;

    this.log('info', 'WebSocketManager初始化完成', { config: this.config });
  }

  /**
   * 日志记录方法
   * @param {string} level - 日志级别
   * @param {string} message - 日志消息
   * @param {Object} data - 附加数据
   */
  log(level, message, data = null) {
    if (!this.logging.verbose && level === 'debug') return;

    const levels = ['debug', 'info', 'warn', 'error'];
    const currentLevelIndex = levels.indexOf(this.logging.level);
    const messageLevelIndex = levels.indexOf(level);

    if (messageLevelIndex >= currentLevelIndex) {
      const logMessage = `WebSocketManager: ${message}`;

      switch (level) {
        case 'debug':
        case 'info':
          console.log(logMessage, data);
          break;
        case 'warn':
          console.warn(logMessage, data);
          break;
        case 'error':
          console.error(logMessage, data);
          break;
      }
    }
  }

  /**
   * 初始化WebSocket管理器
   * 检查登录状态，建立连接
   */
  async init() {
    if (this.isInitialized) {
      this.log('info', '已经初始化，跳过重复初始化');
      return;
    }

    this.log('info', '开始初始化');

    try {
      // 检查token是否存在
      const token = wx.getStorageSync('access_token');
      if (!token) {
        this.log('info', '未找到access_token，等待登录完成');
        // 监听登录完成事件
        this.waitForLogin();
        return;
      }

      // 建立WebSocket连接
      await this.connect();
      this.isInitialized = true;
      this.log('info', '初始化完成');

    } catch (error) {
      this.log('error', '初始化失败', error);
      // 初始化失败时启动重连机制
      this.startReconnect();
    }
  }

  /**
   * 等待登录完成
   */
  waitForLogin() {
    const app = getApp();

    // 如果app有登录Promise，等待其完成
    if (app.globalData.loginInitPromise) {
      app.globalData.loginInitPromise
        .then(() => {
          this.log('info', '登录完成，开始建立连接');
          this.connect();
        })
        .catch((error) => {
          this.log('error', '登录失败，无法建立WebSocket连接', error);
        });
    } else {
      // 定期检查登录状态
      const checkLogin = () => {
        const token = wx.getStorageSync('access_token');
        if (token) {
          this.log('info', '检测到登录完成，开始建立连接');
          this.connect();
        } else {
          // 继续等待
          setTimeout(checkLogin, 1000);
        }
      };
      checkLogin();
    }
  }

  /**
   * 建立WebSocket连接
   */
  async connect() {
    if (this.readyState === this.READY_STATE.CONNECTING ||
        this.readyState === this.READY_STATE.OPEN) {
      this.log('info', '连接已存在或正在连接中');
      return;
    }

    const token = wx.getStorageSync('access_token');
    if (!token) {
      throw new Error('未找到access_token，无法建立WebSocket连接');
    }

    const url = this.config.server.baseUrl + token;
    this.log('info', '开始建立连接', { url: url.replace(token, '***') });

    this.readyState = this.READY_STATE.CONNECTING;

    try {
      this.socketTask = wx.connectSocket({
        url: url,
        protocols: this.config.server.protocols,
        timeout: this.config.server.timeout,
        header: {
          'Authorization': token
        }
      });

      // 设置事件监听器
      this.setupEventListeners();

    } catch (error) {
      this.log('error', '连接建立失败', error);
      this.readyState = this.READY_STATE.CLOSED;
      throw error;
    }
  }

  /**
   * 设置WebSocket事件监听器
   */
  setupEventListeners() {
    if (!this.socketTask) return;

    this.socketTask.onOpen(this.handleOpen);
    this.socketTask.onMessage(this.handleMessage);
    this.socketTask.onClose(this.handleClose);
    this.socketTask.onError(this.handleError);
  }

  /**
   * 处理连接打开事件
   */
  handleOpen(res) {
    console.log('WebSocketManager: 连接已建立', res);
    
    this.readyState = this.READY_STATE.OPEN;
    this.reconnectConfig.currentAttempts = 0;
    this.reconnectConfig.isReconnecting = false;
    
    // 启动心跳
    this.startHeartbeat();
    
    // 处理消息队列
    this.processMessageQueue();
    
    // 触发open事件
    this.emit('open', res);
  }

  /**
   * 处理消息接收事件
   */
  handleMessage(res) {
    console.log('WebSocketManager: 收到消息', res);
    
    try {
      const data = JSON.parse(res.data);
      
      // 检查是否是心跳响应
      if (data.type === 'heartbeat_response' || data.type === 'pong') {
        this.handleHeartbeatResponse();
        return;
      }
      
      // 触发message事件
      this.emit('message', data);
      
    } catch (error) {
      console.error('WebSocketManager: 消息解析失败', error, res.data);
      this.emit('error', { type: 'parse_error', data: res.data, error });
    }
  }

  /**
   * 处理连接关闭事件
   */
  handleClose(res) {
    console.log('WebSocketManager: 连接已关闭', res);
    
    this.readyState = this.READY_STATE.CLOSED;
    this.stopHeartbeat();
    
    // 触发close事件
    this.emit('close', res);
    
    // 如果不是主动关闭，启动重连
    if (!this.reconnectConfig.isReconnecting && res.code !== 1000) {
      this.startReconnect();
    }
  }

  /**
   * 处理连接错误事件
   */
  handleError(res) {
    console.error('WebSocketManager: 连接错误', res);

    this.readyState = this.READY_STATE.CLOSED;
    this.stopHeartbeat();

    // 触发error事件
    this.emit('error', res);

    // 启动重连
    this.startReconnect();
  }

  /**
   * 发送消息
   * @param {Object} messageData - 消息数据
   * @param {string} messageData.topic - 消息主题
   * @param {Object} messageData.data - 消息内容
   */
  sendMessage(messageData) {
    const message = {
      topic: messageData.topic,
      data: messageData.data,
      timestamp: new Date().toISOString(),
      messageId: this.generateMessageId()
    };

    if (this.readyState === this.READY_STATE.OPEN) {
      try {
        this.socketTask.send({
          data: JSON.stringify(message)
        });
        console.log('WebSocketManager: 消息发送成功', message);
        return true;
      } catch (error) {
        console.error('WebSocketManager: 消息发送失败', error);
        // 发送失败时加入队列
        this.messageQueue.push(message);
        return false;
      }
    } else {
      console.log('WebSocketManager: 连接未就绪，消息加入队列', message);
      this.messageQueue.push(message);

      // 如果连接已关闭，尝试重连
      if (this.readyState === this.READY_STATE.CLOSED) {
        this.startReconnect();
      }

      return false;
    }
  }

  /**
   * 发送私聊消息
   * @param {string} type - 消息类型 (text/image)
   * @param {string} content - 消息内容
   * @param {string} receiverId - 接收者ID
   */
  sendPrivateMessage(type, content, receiverId) {
    const userInfo = wx.getStorageSync('userInfo');
    const senderId = userInfo ? userInfo.id : '';

    // 验证消息类型
    if (!this.config.message.supportedTypes.includes(type)) {
      this.log('warn', '不支持的消息类型', { type, supportedTypes: this.config.message.supportedTypes });
      return false;
    }

    return this.sendMessage({
      topic: this.TOPICS.SEND_PRIVATE_MESSAGE,
      data: {
        type: type,
        content: content,
        senderId: senderId,
        receiverId: receiverId
      }
    });
  }

  /**
   * 查询私聊消息
   * @param {string} receiverId - 对方用户ID
   */
  queryPrivateMessage(receiverId) {
    const userInfo = wx.getStorageSync('userInfo');
    const senderId = userInfo ? userInfo.id : '';

    return this.sendMessage({
      topic: this.TOPICS.QUERY_PRIVATE_MESSAGE,
      data: {
        senderId: senderId,
        receiverId: receiverId
      }
    });
  }

  /**
   * 处理消息队列
   */
  processMessageQueue() {
    if (this.messageQueue.length === 0) {
      this.log('debug', '消息队列为空');
      return;
    }

    this.log('info', '处理消息队列', { queueLength: this.messageQueue.length });

    const queue = [...this.messageQueue];
    this.messageQueue.length = 0; // 清空队列

    queue.forEach((message, index) => {
      try {
        this.socketTask.send({
          data: JSON.stringify(message)
        });
        this.log('debug', `队列消息 ${index + 1}/${queue.length} 发送成功`,
          this.logging.logMessageContent ? message : { messageId: message.messageId });
      } catch (error) {
        this.log('error', `队列消息 ${index + 1}/${queue.length} 发送失败`, error);
        // 发送失败的消息重新加入队列（如果队列未满）
        if (this.messageQueue.length < this.maxQueueLength) {
          this.messageQueue.push(message);
        } else {
          this.log('warn', '消息队列已满，丢弃消息', { messageId: message.messageId });
        }
      }
    });
  }

  /**
   * 添加消息到队列
   * @param {Object} message - 消息对象
   */
  addToQueue(message) {
    if (this.messageQueue.length >= this.maxQueueLength) {
      this.log('warn', '消息队列已满，移除最旧的消息');
      this.messageQueue.shift(); // 移除最旧的消息
    }

    this.messageQueue.push(message);
    this.log('debug', '消息已加入队列', {
      messageId: message.messageId,
      queueLength: this.messageQueue.length
    });
  }

  /**
   * 启动心跳机制
   */
  startHeartbeat() {
    this.stopHeartbeat(); // 先停止现有心跳

    console.log('WebSocketManager: 启动心跳机制');

    this.heartbeatConfig.timer = setInterval(() => {
      if (this.readyState === this.READY_STATE.OPEN) {
        this.sendHeartbeat();
      }
    }, this.heartbeatConfig.interval);
  }

  /**
   * 停止心跳机制
   */
  stopHeartbeat() {
    if (this.heartbeatConfig.timer) {
      clearInterval(this.heartbeatConfig.timer);
      this.heartbeatConfig.timer = null;
    }

    if (this.heartbeatConfig.timeoutTimer) {
      clearTimeout(this.heartbeatConfig.timeoutTimer);
      this.heartbeatConfig.timeoutTimer = null;
    }

    this.heartbeatConfig.missedCount = 0;
    console.log('WebSocketManager: 心跳机制已停止');
  }

  /**
   * 发送心跳包
   */
  sendHeartbeat() {
    try {
      const heartbeatMessage = {
        ...this.config.heartbeat.message,
        timestamp: Date.now(),
        messageId: this.generateMessageId()
      };

      this.socketTask.send({
        data: JSON.stringify(heartbeatMessage)
      });

      this.log('debug', '发送心跳包');

      // 设置心跳超时检测
      this.heartbeatConfig.timeoutTimer = setTimeout(() => {
        this.handleHeartbeatTimeout();
      }, this.heartbeatConfig.timeout);

    } catch (error) {
      this.log('error', '心跳包发送失败', error);
      this.handleHeartbeatTimeout();
    }
  }

  /**
   * 处理心跳响应
   */
  handleHeartbeatResponse() {
    this.log('debug', '收到心跳响应');

    // 清除超时定时器
    if (this.heartbeatConfig.timeoutTimer) {
      clearTimeout(this.heartbeatConfig.timeoutTimer);
      this.heartbeatConfig.timeoutTimer = null;
    }

    // 重置丢失计数
    this.heartbeatConfig.missedCount = 0;
  }

  /**
   * 处理心跳超时
   */
  handleHeartbeatTimeout() {
    this.heartbeatConfig.missedCount++;
    console.warn(`WebSocketManager: 心跳超时，连续丢失次数: ${this.heartbeatConfig.missedCount}`);

    if (this.heartbeatConfig.missedCount >= this.heartbeatConfig.maxMissed) {
      console.error('WebSocketManager: 心跳连续丢失过多，认为连接已断开');
      this.close();
      this.startReconnect();
    }
  }

  /**
   * 启动重连机制
   */
  startReconnect() {
    if (this.reconnectConfig.isReconnecting) {
      this.log('info', '重连已在进行中');
      return;
    }

    if (this.reconnectConfig.currentAttempts >= this.reconnectConfig.maxAttempts) {
      this.log('error', '已达到最大重连次数，停止重连');
      this.emit('reconnect', {
        success: false,
        attempts: this.reconnectConfig.currentAttempts,
        reason: 'max_attempts_reached'
      });
      return;
    }

    this.reconnectConfig.isReconnecting = true;
    this.reconnectConfig.currentAttempts++;

    // 计算延迟时间
    let delay;
    if (this.reconnectConfig.useExponentialBackoff) {
      // 指数退避算法
      delay = Math.min(
        this.reconnectConfig.baseDelay * Math.pow(2, this.reconnectConfig.currentAttempts - 1),
        this.reconnectConfig.maxDelay
      );
    } else {
      // 固定延迟
      delay = this.reconnectConfig.baseDelay;
    }

    this.log('info', `开始第 ${this.reconnectConfig.currentAttempts} 次重连`, {
      delay,
      useExponentialBackoff: this.reconnectConfig.useExponentialBackoff
    });

    // 显示重连提示
    wx.showLoading({
      title: `重连中 (${this.reconnectConfig.currentAttempts}/${this.reconnectConfig.maxAttempts})`,
      mask: false
    });

    setTimeout(async () => {
      try {
        await this.connect();
        wx.hideLoading();
        this.log('info', '重连成功');

        this.emit('reconnect', {
          success: true,
          attempts: this.reconnectConfig.currentAttempts
        });

      } catch (error) {
        wx.hideLoading();
        this.log('error', `第 ${this.reconnectConfig.currentAttempts} 次重连失败`, error);

        this.reconnectConfig.isReconnecting = false;

        // 继续尝试重连
        this.startReconnect();
      }
    }, delay);
  }

  /**
   * 关闭WebSocket连接
   * @param {number} code - 关闭代码
   * @param {string} reason - 关闭原因
   */
  close(code = 1000, reason = 'Normal closure') {
    console.log('WebSocketManager: 主动关闭连接', { code, reason });

    this.stopHeartbeat();
    this.reconnectConfig.isReconnecting = false;

    if (this.socketTask && this.readyState !== this.READY_STATE.CLOSED) {
      this.readyState = this.READY_STATE.CLOSING;

      this.socketTask.close({
        code: code,
        reason: reason
      });
    }

    this.readyState = this.READY_STATE.CLOSED;
  }

  /**
   * 销毁WebSocket管理器
   */
  destroy() {
    console.log('WebSocketManager: 销毁管理器');

    this.close();
    this.messageQueue.length = 0;
    this.eventListeners = {
      open: [],
      message: [],
      close: [],
      error: [],
      reconnect: []
    };
    this.isInitialized = false;
  }

  /**
   * 添加事件监听器
   * @param {string} event - 事件名称
   * @param {Function} listener - 监听器函数
   */
  on(event, listener) {
    if (this.eventListeners[event]) {
      this.eventListeners[event].push(listener);
    }
  }

  /**
   * 移除事件监听器
   * @param {string} event - 事件名称
   * @param {Function} listener - 监听器函数
   */
  off(event, listener) {
    if (this.eventListeners[event]) {
      const index = this.eventListeners[event].indexOf(listener);
      if (index > -1) {
        this.eventListeners[event].splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  emit(event, data) {
    if (this.eventListeners[event]) {
      this.eventListeners[event].forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error(`WebSocketManager: 事件监听器执行错误 [${event}]`, error);
        }
      });
    }
  }

  /**
   * 生成消息ID
   */
  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取连接状态
   */
  getReadyState() {
    return this.readyState;
  }

  /**
   * 检查连接是否正常
   */
  isConnected() {
    return this.readyState === this.READY_STATE.OPEN;
  }

  /**
   * 获取连接统计信息
   */
  getStats() {
    return {
      readyState: this.readyState,
      reconnectAttempts: this.reconnectConfig.currentAttempts,
      isReconnecting: this.reconnectConfig.isReconnecting,
      messageQueueLength: this.messageQueue.length,
      heartbeatMissedCount: this.heartbeatConfig.missedCount,
      isInitialized: this.isInitialized
    };
  }

  /**
   * 重置重连计数器
   */
  resetReconnectCounter() {
    this.reconnectConfig.currentAttempts = 0;
    this.reconnectConfig.isReconnecting = false;
  }
}

// 创建单例实例
const websocketManager = new WebSocketManager();

module.exports = websocketManager;
