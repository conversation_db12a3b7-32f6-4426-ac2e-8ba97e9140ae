<!--pages/ws-test/websocket-test.wxml-->
<view class="container">
  <!-- 连接状态 -->
  <view class="status-section">
    <view class="status-item">
      <text class="status-label">连接状态:</text>
      <text class="status-value {{isConnected ? 'connected' : 'disconnected'}}">
        {{connectionStatus}}
      </text>
    </view>
    
    <view class="status-item">
      <text class="status-label">重连次数:</text>
      <text class="status-value">{{stats.reconnectAttempts || 0}}</text>
    </view>
    
    <view class="status-item">
      <text class="status-label">消息队列:</text>
      <text class="status-value">{{stats.messageQueueLength || 0}}</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="button-section">
    <button class="test-button" bindtap="reconnect" disabled="{{isConnected}}">
      手动重连
    </button>
    <button class="test-button" bindtap="sendHeartbeat" disabled="{{!isConnected}}">
      发送心跳
    </button>
    <button class="test-button" bindtap="getConnectionStats">
      查看统计
    </button>
    <button class="test-button" bindtap="clearMessages">
      清空消息
    </button>
  </view>

  <!-- 消息发送 -->
  <view class="send-section">
    <view class="input-group">
      <text class="input-label">接收者ID:</text>
      <input class="input-field" 
             value="{{receiverId}}" 
             bindinput="onReceiverIdChange" 
             placeholder="请输入接收者ID" />
    </view>
    
    <view class="input-group">
      <text class="input-label">消息内容:</text>
      <input class="input-field" 
             value="{{inputText}}" 
             bindinput="onInputChange" 
             placeholder="请输入消息内容" />
    </view>
    
    <view class="send-buttons">
      <button class="send-button" 
              bindtap="sendTestMessage" 
              disabled="{{!isConnected}}">
        发送消息
      </button>
      <button class="send-button" 
              bindtap="queryMessages" 
              disabled="{{!isConnected}}">
        查询历史
      </button>
    </view>
  </view>

  <!-- 消息列表 -->
  <view class="messages-section">
    <view class="messages-header">
      <text class="section-title">消息记录 ({{messages.length}})</text>
    </view>
    
    <scroll-view class="messages-list" scroll-y="true" scroll-top="{{scrollTop}}">
      <view wx:for="{{messages}}" 
            wx:key="id" 
            class="message-item message-{{item.type}}">
        <view class="message-header">
          <text class="message-sender">{{item.sender}}</text>
          <text class="message-time">{{item.time}}</text>
        </view>
        <view class="message-content">{{item.content}}</view>
      </view>
      
      <view wx:if="{{messages.length === 0}}" class="empty-messages">
        暂无消息记录
      </view>
    </scroll-view>
  </view>
</view>
